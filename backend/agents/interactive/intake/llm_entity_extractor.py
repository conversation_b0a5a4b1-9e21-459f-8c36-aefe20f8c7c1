"""
LLM-Powered Entity Extraction for Legal Intake

This module provides sophisticated entity extraction using Large Language Models
to identify and analyze legal entities, relationships, and concepts from client
descriptions with high accuracy and contextual understanding.

Key Features:
- Advanced person and organization identification
- Legal relationship analysis (opposing parties, witnesses, co-defendants)
- Temporal entity extraction (dates, deadlines, court dates)
- Legal concept identification (charges, claims, damages)
- Contextual understanding of entity roles and relationships
- Confidence scoring and uncertainty handling
- Multi-language support and legal terminology recognition
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import PydanticOutputParser
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field

from .state import PracticeArea, IntakeState

logger = logging.getLogger(__name__)


class EntityType(str, Enum):
    """Types of entities that can be extracted."""
    PERSON = "person"
    ORGANIZATION = "organization"
    LEGAL_ENTITY = "legal_entity"
    LOCATION = "location"
    DATE = "date"
    MONETARY_AMOUNT = "monetary_amount"
    LEGAL_CONCEPT = "legal_concept"
    CASE_REFERENCE = "case_reference"
    DOCUMENT = "document"
    VEHICLE = "vehicle"
    PROPERTY = "property"


class EntityRole(str, Enum):
    """Roles that entities can play in legal matters."""
    CLIENT = "client"
    OPPOSING_PARTY = "opposing_party"
    WITNESS = "witness"
    CO_DEFENDANT = "co_defendant"
    VICTIM = "victim"
    FAMILY_MEMBER = "family_member"
    MEDICAL_PROVIDER = "medical_provider"
    INSURANCE_COMPANY = "insurance_company"
    LAW_ENFORCEMENT = "law_enforcement"
    COURT = "court"
    ATTORNEY = "attorney"
    EXPERT_WITNESS = "expert_witness"
    UNKNOWN = "unknown"


class RelationshipType(str, Enum):
    """Types of relationships between entities."""
    ADVERSARIAL = "adversarial"
    FAMILIAL = "familial"
    BUSINESS = "business"
    PROFESSIONAL = "professional"
    CONTRACTUAL = "contractual"
    CRIMINAL = "criminal"
    CIVIL = "civil"
    EMPLOYMENT = "employment"
    MEDICAL = "medical"
    INSURANCE = "insurance"


@dataclass
class ExtractedEntity:
    """Individual extracted entity with detailed analysis."""
    entity_type: EntityType
    name: str
    role: EntityRole
    confidence: float  # 0.0 to 1.0
    context: str  # Context in which entity was mentioned
    
    # Additional attributes
    aliases: List[str] = field(default_factory=list)
    attributes: Dict[str, Any] = field(default_factory=dict)
    relationships: List[Tuple[str, RelationshipType]] = field(default_factory=list)
    
    # Temporal information
    first_mentioned: Optional[str] = None
    relevance_period: Optional[Tuple[datetime, datetime]] = None
    
    # Legal significance
    legal_significance: str = ""
    potential_conflicts: List[str] = field(default_factory=list)
    requires_investigation: bool = False


@dataclass
class EntityExtractionResult:
    """Complete entity extraction result with analysis."""
    entities: List[ExtractedEntity]
    relationships: List[Tuple[str, str, RelationshipType, float]]  # entity1, entity2, relationship, confidence
    timeline: List[Tuple[datetime, str, str]]  # date, event, description
    
    # Analysis insights
    key_parties: List[str]
    potential_conflicts: List[str]
    missing_information: List[str]
    follow_up_questions: List[str]
    
    # Legal context
    practice_area_indicators: Dict[PracticeArea, float]
    complexity_indicators: List[str]
    urgency_indicators: List[str]
    
    # Confidence and quality
    overall_confidence: float
    extraction_quality: str  # "high", "medium", "low"
    llm_reasoning: str


class LLMEntityExtractionOutput(BaseModel):
    """Structured output for LLM entity extraction."""
    entities: List[Dict[str, Any]] = Field(description="List of extracted entities with details")
    relationships: List[Dict[str, Any]] = Field(description="Relationships between entities")
    timeline_events: List[Dict[str, Any]] = Field(description="Chronological events and dates")
    key_parties: List[str] = Field(description="Most important parties in the matter")
    potential_conflicts: List[str] = Field(description="Potential conflict of interest indicators")
    missing_information: List[str] = Field(description="Important information that appears to be missing")
    follow_up_questions: List[str] = Field(description="Questions to gather missing information")
    practice_area_indicators: Dict[str, float] = Field(description="Practice area probability scores")
    complexity_indicators: List[str] = Field(description="Factors indicating case complexity")
    urgency_indicators: List[str] = Field(description="Time-sensitive factors requiring attention")
    overall_confidence: float = Field(description="Overall confidence in extraction (0.0-1.0)")
    reasoning: str = Field(description="Detailed reasoning for the extraction and analysis")


class LLMEntityExtractor:
    """
    Advanced LLM-powered entity extraction for legal intake.
    
    Uses sophisticated language models to identify, analyze, and understand
    legal entities and their relationships with high accuracy and contextual
    understanding.
    """
    
    def __init__(self, llm_model: str = "gpt-4", temperature: float = 0.1):
        """Initialize the LLM entity extractor."""
        self.llm = ChatOpenAI(model=llm_model, temperature=temperature)
        self.output_parser = PydanticOutputParser(pydantic_object=LLMEntityExtractionOutput)
        
        self.extraction_prompt = ChatPromptTemplate.from_messages([
            ("system", self._get_system_prompt()),
            ("human", self._get_human_prompt())
        ])
    
    async def extract_entities(
        self, 
        description: str, 
        practice_area: Optional[PracticeArea] = None,
        additional_context: Dict[str, Any] = None
    ) -> EntityExtractionResult:
        """
        Extract entities from legal case description using LLM analysis.
        
        Args:
            description: Case description from client
            practice_area: Known practice area (if any)
            additional_context: Additional context information
            
        Returns:
            EntityExtractionResult with comprehensive entity analysis
        """
        if not description or len(description.strip()) < 10:
            return self._empty_extraction_result()
        
        additional_context = additional_context or {}
        
        try:
            # Prepare context for LLM
            context_str = self._format_context(practice_area, additional_context)
            
            # Create the prompt
            prompt = self.extraction_prompt.format_prompt(
                description=description,
                context=context_str,
                format_instructions=self.output_parser.get_format_instructions()
            )
            
            # Get LLM response
            response = await self.llm.ainvoke(prompt.to_messages())
            
            # Parse structured output
            parsed_result = self.output_parser.parse(response.content)
            
            # Convert to our internal format
            extraction_result = self._convert_llm_output(parsed_result, description)
            
            logger.info(f"Extracted {len(extraction_result.entities)} entities with {extraction_result.overall_confidence:.2f} confidence")
            
            return extraction_result
            
        except Exception as e:
            logger.error(f"Error during LLM entity extraction: {str(e)}")
            return self._error_extraction_result(str(e))
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for entity extraction."""
        return """You are an expert legal analyst specializing in entity extraction and relationship analysis for legal intake. Your task is to analyze client descriptions of legal matters and extract all relevant entities, relationships, and contextual information with high precision.

ENTITY TYPES TO IDENTIFY:
- PERSONS: Clients, opposing parties, witnesses, family members, professionals
- ORGANIZATIONS: Companies, insurance companies, medical providers, law firms
- LEGAL_ENTITIES: Courts, government agencies, law enforcement
- LOCATIONS: Addresses, jurisdictions, incident locations
- DATES: Incident dates, court dates, deadlines, statute of limitations
- MONETARY_AMOUNTS: Damages, settlements, costs, fees
- LEGAL_CONCEPTS: Charges, claims, causes of action, legal theories
- CASE_REFERENCES: Case numbers, docket numbers, citations
- DOCUMENTS: Contracts, reports, medical records, correspondence
- VEHICLES: Cars, trucks, motorcycles involved in incidents
- PROPERTY: Real estate, personal property involved in disputes

ENTITY ROLES TO DETERMINE:
- CLIENT: The person seeking legal representation
- OPPOSING_PARTY: Adverse parties in the matter
- WITNESS: People who observed relevant events
- CO_DEFENDANT: Joint defendants in criminal matters
- VICTIM: Injured parties or crime victims
- FAMILY_MEMBER: Relatives involved in family law matters
- MEDICAL_PROVIDER: Doctors, hospitals, healthcare providers
- INSURANCE_COMPANY: Insurance carriers and adjusters
- LAW_ENFORCEMENT: Police, investigators, prosecutors
- COURT: Judicial bodies and court personnel
- ATTORNEY: Legal counsel (current or previous)

RELATIONSHIP ANALYSIS:
- Identify relationships between entities (adversarial, familial, business, etc.)
- Determine potential conflicts of interest
- Analyze power dynamics and legal significance

TEMPORAL ANALYSIS:
- Extract all dates and create timeline of events
- Identify time-sensitive factors and deadlines
- Assess urgency based on temporal factors

LEGAL CONTEXT ANALYSIS:
- Determine practice area indicators
- Assess case complexity factors
- Identify urgency indicators
- Spot potential ethical issues

QUALITY REQUIREMENTS:
- Provide confidence scores for all extractions
- Explain reasoning for classifications
- Identify missing information
- Suggest follow-up questions
- Maintain high accuracy and legal precision"""

    def _get_human_prompt(self) -> str:
        """Get the human prompt template."""
        return """Please analyze this legal matter description and extract all relevant entities, relationships, and contextual information:

CASE DESCRIPTION:
{description}

ADDITIONAL CONTEXT:
{context}

Perform comprehensive entity extraction and analysis following these steps:

1. ENTITY IDENTIFICATION: Identify all persons, organizations, locations, dates, amounts, legal concepts, and other relevant entities
2. ROLE CLASSIFICATION: Determine the role each entity plays in the legal matter
3. RELATIONSHIP ANALYSIS: Identify relationships between entities and their legal significance
4. TEMPORAL ANALYSIS: Create timeline of events and identify time-sensitive factors
5. CONFLICT ASSESSMENT: Identify potential conflicts of interest
6. PRACTICE AREA ANALYSIS: Determine practice area indicators and probability scores
7. COMPLEXITY ASSESSMENT: Identify factors that indicate case complexity
8. URGENCY ASSESSMENT: Identify time-sensitive factors requiring immediate attention
9. INFORMATION GAPS: Identify missing information and suggest follow-up questions

Provide your analysis in the following structured format:
{format_instructions}

Focus on legal accuracy, contextual understanding, and practical utility for legal intake processing."""

    def _format_context(
        self,
        practice_area: Optional[PracticeArea],
        additional_context: Dict[str, Any]
    ) -> str:
        """Format context information for the LLM prompt."""
        context_parts = []

        if practice_area:
            context_parts.append(f"Known Practice Area: {practice_area.value}")

        for key, value in additional_context.items():
            if value is not None:
                context_parts.append(f"{key.replace('_', ' ').title()}: {value}")

        return "\n".join(context_parts) if context_parts else "No additional context provided."

    def _convert_llm_output(
        self,
        llm_output: LLMEntityExtractionOutput,
        original_description: str
    ) -> EntityExtractionResult:
        """Convert LLM output to our internal EntityExtractionResult format."""

        # Convert entities
        entities = []
        for entity_data in llm_output.entities:
            try:
                entity = ExtractedEntity(
                    entity_type=EntityType(entity_data.get("type", "person")),
                    name=entity_data.get("name", ""),
                    role=EntityRole(entity_data.get("role", "unknown")),
                    confidence=entity_data.get("confidence", 0.5),
                    context=entity_data.get("context", ""),
                    aliases=entity_data.get("aliases", []),
                    attributes=entity_data.get("attributes", {}),
                    legal_significance=entity_data.get("legal_significance", ""),
                    potential_conflicts=entity_data.get("potential_conflicts", []),
                    requires_investigation=entity_data.get("requires_investigation", False)
                )
                entities.append(entity)
            except (ValueError, KeyError) as e:
                logger.warning(f"Error converting entity: {e}")
                continue

        # Convert relationships
        relationships = []
        for rel_data in llm_output.relationships:
            try:
                relationship = (
                    rel_data.get("entity1", ""),
                    rel_data.get("entity2", ""),
                    RelationshipType(rel_data.get("type", "unknown")),
                    rel_data.get("confidence", 0.5)
                )
                relationships.append(relationship)
            except (ValueError, KeyError) as e:
                logger.warning(f"Error converting relationship: {e}")
                continue

        # Convert timeline events
        timeline = []
        for event_data in llm_output.timeline_events:
            try:
                # Parse date string to datetime (simplified)
                date_str = event_data.get("date", "")
                event_date = self._parse_date_string(date_str)
                if event_date:
                    timeline.append((
                        event_date,
                        event_data.get("event", ""),
                        event_data.get("description", "")
                    ))
            except Exception as e:
                logger.warning(f"Error converting timeline event: {e}")
                continue

        # Convert practice area indicators
        practice_area_indicators = {}
        for area_str, score in llm_output.practice_area_indicators.items():
            try:
                practice_area_indicators[PracticeArea(area_str)] = score
            except ValueError:
                logger.warning(f"Unknown practice area: {area_str}")
                continue

        return EntityExtractionResult(
            entities=entities,
            relationships=relationships,
            timeline=timeline,
            key_parties=llm_output.key_parties,
            potential_conflicts=llm_output.potential_conflicts,
            missing_information=llm_output.missing_information,
            follow_up_questions=llm_output.follow_up_questions,
            practice_area_indicators=practice_area_indicators,
            complexity_indicators=llm_output.complexity_indicators,
            urgency_indicators=llm_output.urgency_indicators,
            overall_confidence=llm_output.overall_confidence,
            extraction_quality=self._assess_extraction_quality(llm_output.overall_confidence),
            llm_reasoning=llm_output.reasoning
        )

    def _parse_date_string(self, date_str: str) -> Optional[datetime]:
        """Parse date string to datetime object."""
        if not date_str:
            return None

        # Common date formats
        date_formats = [
            "%Y-%m-%d",
            "%m/%d/%Y",
            "%m-%d-%Y",
            "%B %d, %Y",
            "%b %d, %Y",
            "%d %B %Y",
            "%d %b %Y"
        ]

        for fmt in date_formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue

        logger.warning(f"Could not parse date: {date_str}")
        return None

    def _assess_extraction_quality(self, confidence: float) -> str:
        """Assess extraction quality based on confidence score."""
        if confidence >= 0.8:
            return "high"
        elif confidence >= 0.6:
            return "medium"
        else:
            return "low"

    def _empty_extraction_result(self) -> EntityExtractionResult:
        """Return empty extraction result for invalid input."""
        return EntityExtractionResult(
            entities=[],
            relationships=[],
            timeline=[],
            key_parties=[],
            potential_conflicts=[],
            missing_information=["Insufficient description provided"],
            follow_up_questions=["Please provide more details about your legal matter"],
            practice_area_indicators={},
            complexity_indicators=[],
            urgency_indicators=[],
            overall_confidence=0.0,
            extraction_quality="low",
            llm_reasoning="Insufficient information provided for analysis"
        )

    def _error_extraction_result(self, error_message: str) -> EntityExtractionResult:
        """Return error extraction result when LLM fails."""
        return EntityExtractionResult(
            entities=[],
            relationships=[],
            timeline=[],
            key_parties=[],
            potential_conflicts=["System error during analysis"],
            missing_information=["Analysis failed due to system error"],
            follow_up_questions=["Please try again or contact support"],
            practice_area_indicators={},
            complexity_indicators=["System analysis error"],
            urgency_indicators=[],
            overall_confidence=0.0,
            extraction_quality="low",
            llm_reasoning=f"Entity extraction failed: {error_message}"
        )
