"""
Enhanced Risk Assessment Engine for Legal Intake

This module provides comprehensive risk assessment algorithms that evaluate:
- Case complexity and difficulty factors
- Urgency and time-sensitive elements
- Potential conflicts and ethical considerations
- Practice-area specific risk indicators
- Financial and resource implications
- Success probability and strategic considerations

Key Features:
- Multi-dimensional risk scoring
- Practice-area specific risk models
- Temporal risk analysis (deadlines, SOL)
- Conflict risk assessment
- Resource requirement estimation
- Success probability modeling
- Detailed risk reporting with mitigation strategies
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import math

from .state import (
    PracticeArea,
    WorkType,
    CaseUrgency,
    IntakeState,
    PersonalInjuryCaseType,
    FamilyLawCaseType,
    CriminalDefenseCaseType
)
# LLM imports with lazy loading to avoid circular dependencies

logger = logging.getLogger(__name__)


class RiskLevel(str, Enum):
    """Risk levels for different assessment categories."""
    VERY_LOW = "very_low"      # 0-20%: Minimal risk
    LOW = "low"                # 21-40%: Low risk, standard handling
    MEDIUM = "medium"          # 41-60%: Moderate risk, requires attention
    HIGH = "high"              # 61-80%: High risk, requires careful management
    VERY_HIGH = "very_high"    # 81-100%: Very high risk, may decline


class RiskCategory(str, Enum):
    """Categories of risk assessment."""
    COMPLEXITY = "complexity"              # Case complexity and difficulty
    URGENCY = "urgency"                   # Time-sensitive factors
    CONFLICT = "conflict"                 # Conflict of interest risks
    FINANCIAL = "financial"               # Financial and resource risks
    SUCCESS = "success"                   # Success probability assessment
    ETHICAL = "ethical"                   # Ethical considerations
    RESOURCE = "resource"                 # Resource requirement assessment


@dataclass
class RiskFactor:
    """Individual risk factor with scoring and impact."""
    category: RiskCategory
    factor_name: str
    description: str
    risk_score: float  # 0.0 to 1.0
    weight: float      # Importance weight (0.0 to 1.0)
    impact_description: str
    mitigation_strategies: List[str] = field(default_factory=list)
    requires_attention: bool = False


@dataclass
class RiskAssessmentResult:
    """Comprehensive risk assessment result."""
    overall_risk_score: float  # 0.0 to 1.0
    overall_risk_level: RiskLevel
    category_scores: Dict[RiskCategory, float]
    category_levels: Dict[RiskCategory, RiskLevel]
    
    # Detailed risk factors
    risk_factors: List[RiskFactor]
    high_risk_factors: List[RiskFactor]
    
    # Recommendations
    recommendation: str  # "accept", "accept_with_conditions", "decline", "refer"
    conditions: List[str] = field(default_factory=list)
    mitigation_strategies: List[str] = field(default_factory=list)
    
    # Resource estimates
    estimated_hours: Optional[int] = None
    estimated_complexity: Optional[str] = None  # "low", "medium", "high", "very_high"
    success_probability: Optional[float] = None  # 0.0 to 1.0
    
    # Urgency assessment
    requires_immediate_attention: bool = False
    deadline_risks: List[str] = field(default_factory=list)
    
    # Practice area specific insights
    practice_area_insights: Dict[str, Any] = field(default_factory=dict)


class RiskAssessmentEngine:
    """
    Comprehensive risk assessment engine for legal intake.
    
    Evaluates multiple dimensions of risk to provide detailed analysis
    and recommendations for case acceptance and handling.
    """
    
    def __init__(self, use_llm: bool = True):
        """Initialize the risk assessment engine with LLM integration."""
        self.use_llm = use_llm

        # Initialize LLM components (lazy import to avoid circular dependencies)
        if use_llm:
            try:
                from .llm_risk_intelligence import LLMRiskIntelligence
                from .llm_success_predictor import LLMSuccessPredictor
                self.llm_risk_intelligence = LLMRiskIntelligence()
                self.llm_success_predictor = LLMSuccessPredictor()
            except ImportError as e:
                logger.warning(f"Could not import LLM components: {e}")
                self.use_llm = False

        self.practice_area_models = {
            PracticeArea.PERSONAL_INJURY: self._personal_injury_risk_model,
            PracticeArea.FAMILY_LAW: self._family_law_risk_model,
            PracticeArea.CRIMINAL_DEFENSE: self._criminal_defense_risk_model
        }

        # Risk scoring weights by category
        self.category_weights = {
            RiskCategory.COMPLEXITY: 0.20,
            RiskCategory.URGENCY: 0.15,
            RiskCategory.CONFLICT: 0.25,
            RiskCategory.FINANCIAL: 0.15,
            RiskCategory.SUCCESS: 0.15,
            RiskCategory.ETHICAL: 0.10
        }
    
    async def assess_risk(
        self,
        state: IntakeState,
        conflict_results: Optional[Any] = None,
        entity_extraction: Optional[Any] = None,
        conflict_analyses: Optional[List[Any]] = None
    ) -> RiskAssessmentResult:
        """
        Perform comprehensive risk assessment for the intake with LLM intelligence.

        Args:
            state: Current intake state
            conflict_results: Results from conflict checking
            entity_extraction: Results from LLM entity extraction
            conflict_analyses: Results from LLM conflict analysis

        Returns:
            RiskAssessmentResult with detailed risk analysis
        """
        try:
            # Use LLM-powered analysis if available
            if self.use_llm and entity_extraction:
                # LLM-powered risk intelligence
                llm_risk_assessment = await self.llm_risk_intelligence.assess_risk(
                    state, entity_extraction, conflict_analyses
                )

                # LLM-powered success prediction
                success_prediction = await self.llm_success_predictor.predict_success(
                    state, entity_extraction, conflict_analyses
                )

                # Convert LLM results to our format
                return self._convert_llm_risk_assessment(
                    llm_risk_assessment, success_prediction, state
                )

            else:
                # Fallback to traditional rule-based assessment
                risk_factors = []

                # Assess complexity risks
                complexity_factors = await self._assess_complexity_risk(state)
                risk_factors.extend(complexity_factors)

                # Assess urgency risks
                urgency_factors = await self._assess_urgency_risk(state)
                risk_factors.extend(urgency_factors)

                # Assess conflict risks
                conflict_factors = await self._assess_conflict_risk(state, conflict_results)
                risk_factors.extend(conflict_factors)

                # Assess financial risks
                financial_factors = await self._assess_financial_risk(state)
                risk_factors.extend(financial_factors)

                # Assess success probability
                success_factors = await self._assess_success_risk(state)
                risk_factors.extend(success_factors)

                # Assess ethical risks
                ethical_factors = await self._assess_ethical_risk(state)
                risk_factors.extend(ethical_factors)

                # Apply practice area specific risk model
                if state.matter.practice_area in self.practice_area_models:
                    model_func = self.practice_area_models[state.matter.practice_area]
                    area_factors = await model_func(state)
                    risk_factors.extend(area_factors)
            
            # Calculate category scores
            category_scores = self._calculate_category_scores(risk_factors)
            category_levels = {
                category: self._score_to_risk_level(score)
                for category, score in category_scores.items()
            }
            
            # Calculate overall risk score
            overall_score = self._calculate_overall_risk_score(category_scores)
            overall_level = self._score_to_risk_level(overall_score)
            
            # Generate recommendations
            recommendation, conditions = self._generate_recommendation(
                overall_score, category_scores, risk_factors
            )
            
            # Extract high-risk factors
            high_risk_factors = [
                factor for factor in risk_factors 
                if factor.risk_score > 0.7 or factor.requires_attention
            ]
            
            # Generate mitigation strategies
            mitigation_strategies = self._generate_mitigation_strategies(risk_factors)
            
            # Estimate resources and complexity
            estimated_hours, estimated_complexity = self._estimate_resources(state, risk_factors)
            success_probability = self._calculate_success_probability(risk_factors)
            
            # Check for immediate attention requirements
            requires_immediate = self._requires_immediate_attention(state, risk_factors)
            deadline_risks = self._assess_deadline_risks(state)
            
            # Generate practice area insights
            practice_insights = self._generate_practice_area_insights(state, risk_factors)
            
            return RiskAssessmentResult(
                overall_risk_score=overall_score,
                overall_risk_level=overall_level,
                category_scores=category_scores,
                category_levels=category_levels,
                risk_factors=risk_factors,
                high_risk_factors=high_risk_factors,
                recommendation=recommendation,
                conditions=conditions,
                mitigation_strategies=mitigation_strategies,
                estimated_hours=estimated_hours,
                estimated_complexity=estimated_complexity,
                success_probability=success_probability,
                requires_immediate_attention=requires_immediate,
                deadline_risks=deadline_risks,
                practice_area_insights=practice_insights
            )
            
        except Exception as e:
            logger.error(f"Error during risk assessment: {str(e)}")
            # Return high-risk result on error
            return self._error_risk_result(str(e))

    def _convert_llm_risk_assessment(
        self,
        llm_risk: Any,
        success_prediction: Any,
        state: IntakeState
    ) -> RiskAssessmentResult:
        """Convert LLM risk assessment to RiskAssessmentResult."""

        # Convert LLM risk factors to our format
        risk_factors = []
        for llm_factor in llm_risk.risk_factors:
            risk_factor = RiskFactor(
                category=RiskCategory(llm_factor.get("category", "complexity")),
                factor_name=llm_factor.get("name", "Unknown Factor"),
                description=llm_factor.get("description", ""),
                risk_score=llm_factor.get("risk_score", 0.5),
                weight=llm_factor.get("weight", 0.5),
                impact_description=llm_factor.get("impact", ""),
                mitigation_strategies=llm_factor.get("mitigation_strategies", []),
                requires_attention=llm_factor.get("requires_attention", False)
            )
            risk_factors.append(risk_factor)

        # Calculate category scores from LLM assessment
        category_scores = {}
        category_levels = {}

        for category in RiskCategory:
            # Use LLM's category-specific analysis
            if category.value in llm_risk.complexity_analysis:
                score = llm_risk.complexity_analysis.get("risk_score", 0.5)
            elif category.value in llm_risk.financial_analysis:
                score = llm_risk.financial_analysis.get("risk_score", 0.5)
            elif category.value in llm_risk.strategic_analysis:
                score = llm_risk.strategic_analysis.get("risk_score", 0.5)
            else:
                # Calculate from risk factors
                category_factors = [f for f in risk_factors if f.category == category]
                if category_factors:
                    score = sum(f.risk_score * f.weight for f in category_factors) / sum(f.weight for f in category_factors)
                else:
                    score = 0.0

            category_scores[category] = score
            category_levels[category] = self._score_to_risk_level(score)

        # Extract high-risk factors
        high_risk_factors = [f for f in risk_factors if f.risk_score > 0.7 or f.requires_attention]

        return RiskAssessmentResult(
            overall_risk_score=llm_risk.overall_risk_score,
            overall_risk_level=llm_risk.overall_risk_level,
            category_scores=category_scores,
            category_levels=category_levels,
            risk_factors=risk_factors,
            high_risk_factors=high_risk_factors,
            recommendation=llm_risk.recommendation,
            conditions=llm_risk.conditions,
            mitigation_strategies=llm_risk.mitigation_strategies,
            estimated_hours=int(llm_risk.estimated_cost_range[1] / 300) if llm_risk.estimated_cost_range[1] > 0 else None,  # Assume $300/hour
            estimated_complexity=llm_risk.resource_requirements.value,
            success_probability=success_prediction.success_probability,
            requires_immediate_attention=any(f.requires_attention for f in risk_factors),
            deadline_risks=llm_risk.next_steps,  # Use next steps as deadline risks
            practice_area_insights=llm_risk.practice_area_factors
        )

    def _score_to_risk_level(self, score: float) -> RiskLevel:
        """Convert numeric score to risk level."""
        if score <= 0.2:
            return RiskLevel.VERY_LOW
        elif score <= 0.4:
            return RiskLevel.LOW
        elif score <= 0.6:
            return RiskLevel.MEDIUM
        elif score <= 0.8:
            return RiskLevel.HIGH
        else:
            return RiskLevel.VERY_HIGH

    async def _assess_complexity_risk(self, state: IntakeState) -> List[RiskFactor]:
        """Assess case complexity risk factors."""
        factors = []

        # Case type complexity
        if state.matter.practice_area == PracticeArea.PERSONAL_INJURY:
            if hasattr(state.matter, 'case_type'):
                if state.matter.case_type == PersonalInjuryCaseType.MEDICAL_MALPRACTICE:
                    factors.append(RiskFactor(
                        category=RiskCategory.COMPLEXITY,
                        factor_name="Medical Malpractice Complexity",
                        description="Medical malpractice cases require specialized expertise",
                        risk_score=0.8,
                        weight=0.9,
                        impact_description="High complexity requiring medical expert witnesses",
                        mitigation_strategies=["Engage medical experts early", "Partner with medical malpractice specialist"],
                        requires_attention=True
                    ))
                elif state.matter.case_type == PersonalInjuryCaseType.PRODUCT_LIABILITY:
                    factors.append(RiskFactor(
                        category=RiskCategory.COMPLEXITY,
                        factor_name="Product Liability Complexity",
                        description="Product liability cases involve complex technical analysis",
                        risk_score=0.7,
                        weight=0.8,
                        impact_description="Requires technical experts and extensive discovery",
                        mitigation_strategies=["Engage technical experts", "Plan for extensive discovery"]
                    ))

        # Multiple injuries or complex damages
        if state.matter.injuries and len(state.matter.injuries) > 3:
            factors.append(RiskFactor(
                category=RiskCategory.COMPLEXITY,
                factor_name="Multiple Injuries",
                description="Multiple injuries increase case complexity",
                risk_score=0.6,
                weight=0.7,
                impact_description="Requires coordination of multiple medical providers",
                mitigation_strategies=["Organize medical records systematically", "Consider medical case manager"]
            ))

        # Complexity score from state
        if state.matter.complexity_score and state.matter.complexity_score > 7:
            factors.append(RiskFactor(
                category=RiskCategory.COMPLEXITY,
                factor_name="High Complexity Score",
                description=f"Case complexity score: {state.matter.complexity_score}/10",
                risk_score=state.matter.complexity_score / 10.0,
                weight=0.8,
                impact_description="High complexity case requiring specialized handling",
                mitigation_strategies=["Assign senior attorney", "Allocate additional resources"]
            ))

        return factors

    async def _assess_urgency_risk(self, state: IntakeState) -> List[RiskFactor]:
        """Assess urgency and time-sensitive risk factors."""
        factors = []

        # Statute of limitations urgency
        if state.matter.statute_of_limitations:
            days_until_sol = (state.matter.statute_of_limitations - datetime.now()).days
            if days_until_sol < 30:
                factors.append(RiskFactor(
                    category=RiskCategory.URGENCY,
                    factor_name="Statute of Limitations Urgency",
                    description=f"SOL expires in {days_until_sol} days",
                    risk_score=max(0.9 - (days_until_sol / 30.0), 0.1),
                    weight=1.0,
                    impact_description="Critical deadline approaching",
                    mitigation_strategies=["File immediately", "Prioritize case preparation"],
                    requires_attention=True
                ))

        # Court date urgency (criminal defense)
        if (state.matter.practice_area == PracticeArea.CRIMINAL_DEFENSE and
            state.matter.court_date):
            days_until_court = (state.matter.court_date - datetime.now()).days
            if days_until_court < 7:
                factors.append(RiskFactor(
                    category=RiskCategory.URGENCY,
                    factor_name="Imminent Court Date",
                    description=f"Court date in {days_until_court} days",
                    risk_score=0.9,
                    weight=1.0,
                    impact_description="Immediate preparation required",
                    mitigation_strategies=["Immediate case preparation", "Request continuance if needed"],
                    requires_attention=True
                ))

        # Case urgency level
        urgency_scores = {
            CaseUrgency.CRITICAL: 0.9,
            CaseUrgency.HIGH: 0.7,
            CaseUrgency.MEDIUM: 0.5,
            CaseUrgency.LOW: 0.2
        }

        if state.matter.urgency in urgency_scores:
            factors.append(RiskFactor(
                category=RiskCategory.URGENCY,
                factor_name=f"Case Urgency: {state.matter.urgency.value.title()}",
                description=f"Case marked as {state.matter.urgency.value} urgency",
                risk_score=urgency_scores[state.matter.urgency],
                weight=0.8,
                impact_description=f"Requires {state.matter.urgency.value} priority handling",
                mitigation_strategies=["Prioritize in case queue", "Allocate appropriate resources"]
            ))

        return factors

    async def _assess_conflict_risk(
        self,
        state: IntakeState,
        conflict_results: Optional[Any]
    ) -> List[RiskFactor]:
        """Assess conflict of interest risk factors."""
        factors = []

        if not conflict_results:
            return factors

        if conflict_results.has_conflicts:
            for conflict in conflict_results.conflicts:
                severity_scores = {
                    "critical": 0.9,
                    "high": 0.7,
                    "medium": 0.5,
                    "low": 0.3
                }

                severity = conflict.get("severity", "medium")
                risk_score = severity_scores.get(severity, 0.5)

                factors.append(RiskFactor(
                    category=RiskCategory.CONFLICT,
                    factor_name=f"Conflict: {conflict.get('conflict_type', 'Unknown')}",
                    description=conflict.get("description", "Conflict detected"),
                    risk_score=risk_score,
                    weight=0.9,
                    impact_description="Potential conflict of interest",
                    mitigation_strategies=["Review conflict details", "Consider waiver or decline"],
                    requires_attention=severity in ["critical", "high"]
                ))

        return factors

    async def _assess_financial_risk(self, state: IntakeState) -> List[RiskFactor]:
        """Assess financial and resource risk factors."""
        factors = []

        # Low estimated value cases
        if state.matter.estimated_value and state.matter.estimated_value < 10000:
            factors.append(RiskFactor(
                category=RiskCategory.FINANCIAL,
                factor_name="Low Case Value",
                description=f"Estimated value: ${state.matter.estimated_value:,.2f}",
                risk_score=0.6,
                weight=0.7,
                impact_description="May not justify resource investment",
                mitigation_strategies=["Consider flat fee arrangement", "Streamline case handling"]
            ))

        # High complexity with uncertain value
        if (state.matter.complexity_score and state.matter.complexity_score > 7 and
            not state.matter.estimated_value):
            factors.append(RiskFactor(
                category=RiskCategory.FINANCIAL,
                factor_name="High Complexity, Unknown Value",
                description="Complex case with uncertain financial outcome",
                risk_score=0.7,
                weight=0.8,
                impact_description="Resource investment risk",
                mitigation_strategies=["Conduct thorough case evaluation", "Consider retainer requirements"]
            ))

        return factors

    async def _assess_success_risk(self, state: IntakeState) -> List[RiskFactor]:
        """Assess success probability risk factors."""
        factors = []

        # Weak liability cases (personal injury)
        if (state.matter.practice_area == PracticeArea.PERSONAL_INJURY and
            state.matter.description and "unclear liability" in state.matter.description.lower()):
            factors.append(RiskFactor(
                category=RiskCategory.SUCCESS,
                factor_name="Unclear Liability",
                description="Liability may be disputed or unclear",
                risk_score=0.6,
                weight=0.8,
                impact_description="Reduced success probability",
                mitigation_strategies=["Investigate liability thoroughly", "Consider case strength"]
            ))

        # Criminal cases with serious charges
        if (state.matter.practice_area == PracticeArea.CRIMINAL_DEFENSE and
            state.matter.charges and any("felony" in charge.lower() for charge in state.matter.charges)):
            factors.append(RiskFactor(
                category=RiskCategory.SUCCESS,
                factor_name="Serious Criminal Charges",
                description="Felony charges carry significant consequences",
                risk_score=0.7,
                weight=0.9,
                impact_description="High stakes case requiring expert handling",
                mitigation_strategies=["Assign experienced criminal attorney", "Prepare comprehensive defense"]
            ))

        return factors

    async def _assess_ethical_risk(self, state: IntakeState) -> List[RiskFactor]:
        """Assess ethical risk factors."""
        factors = []

        # Domestic violence cases (potential dual representation issues)
        if (state.matter.practice_area == PracticeArea.FAMILY_LAW and
            state.matter.domestic_violence_involved):
            factors.append(RiskFactor(
                category=RiskCategory.ETHICAL,
                factor_name="Domestic Violence Considerations",
                description="Domestic violence cases require special ethical considerations",
                risk_score=0.5,
                weight=0.8,
                impact_description="Special handling and safety considerations required",
                mitigation_strategies=["Follow DV protocols", "Ensure client safety", "Consider referral to specialist"]
            ))

        # Criminal cases with co-defendants
        if (state.matter.practice_area == PracticeArea.CRIMINAL_DEFENSE and
            state.matter.description and "co-defendant" in state.matter.description.lower()):
            factors.append(RiskFactor(
                category=RiskCategory.ETHICAL,
                factor_name="Co-Defendant Issues",
                description="Potential conflicts with co-defendants",
                risk_score=0.7,
                weight=0.9,
                impact_description="May require separate counsel for co-defendants",
                mitigation_strategies=["Assess conflict potential", "Consider separate representation"],
                requires_attention=True
            ))

        return factors

    async def _personal_injury_risk_model(self, state: IntakeState) -> List[RiskFactor]:
        """Personal injury specific risk assessment."""
        factors = []

        # Insurance involvement
        if not state.matter.insurance_involved:
            factors.append(RiskFactor(
                category=RiskCategory.FINANCIAL,
                factor_name="No Insurance Coverage",
                description="No insurance coverage identified",
                risk_score=0.8,
                weight=0.9,
                impact_description="Collection risk for damages",
                mitigation_strategies=["Investigate all potential coverage", "Assess defendant assets"],
                requires_attention=True
            ))

        # Medical treatment status
        if not state.matter.medical_treatment:
            factors.append(RiskFactor(
                category=RiskCategory.SUCCESS,
                factor_name="No Medical Treatment",
                description="Client has not sought medical treatment",
                risk_score=0.6,
                weight=0.7,
                impact_description="May affect damages claim",
                mitigation_strategies=["Encourage medical evaluation", "Document reasons for no treatment"]
            ))

        return factors

    async def _family_law_risk_model(self, state: IntakeState) -> List[RiskFactor]:
        """Family law specific risk assessment."""
        factors = []

        # High-asset cases
        if state.matter.assets_involved and state.matter.estimated_value and state.matter.estimated_value > 500000:
            factors.append(RiskFactor(
                category=RiskCategory.COMPLEXITY,
                factor_name="High-Asset Divorce",
                description="High-value assets require specialized handling",
                risk_score=0.6,
                weight=0.8,
                impact_description="Requires asset valuation and complex division",
                mitigation_strategies=["Engage financial experts", "Consider collaborative approach"]
            ))

        # Children involved
        if state.matter.children_involved:
            factors.append(RiskFactor(
                category=RiskCategory.COMPLEXITY,
                factor_name="Child Custody Issues",
                description="Children involved in family law matter",
                risk_score=0.5,
                weight=0.8,
                impact_description="Requires child-focused approach and potential evaluations",
                mitigation_strategies=["Consider child's best interests", "May need custody evaluation"]
            ))

        return factors

    async def _criminal_defense_risk_model(self, state: IntakeState) -> List[RiskFactor]:
        """Criminal defense specific risk assessment."""
        factors = []

        # Recent arrest
        if (state.matter.arrest_date and
            (datetime.now() - state.matter.arrest_date).days < 3):
            factors.append(RiskFactor(
                category=RiskCategory.URGENCY,
                factor_name="Recent Arrest",
                description="Client recently arrested",
                risk_score=0.8,
                weight=0.9,
                impact_description="Time-sensitive case requiring immediate attention",
                mitigation_strategies=["Immediate case review", "Address bail/release issues"],
                requires_attention=True
            ))

        # Multiple charges
        if state.matter.charges and len(state.matter.charges) > 3:
            factors.append(RiskFactor(
                category=RiskCategory.COMPLEXITY,
                factor_name="Multiple Charges",
                description=f"{len(state.matter.charges)} charges filed",
                risk_score=0.6,
                weight=0.7,
                impact_description="Complex case with multiple legal issues",
                mitigation_strategies=["Prioritize most serious charges", "Consider plea negotiations"]
            ))

        return factors

    def _calculate_category_scores(self, risk_factors: List[RiskFactor]) -> Dict[RiskCategory, float]:
        """Calculate risk scores for each category."""
        category_scores = {}

        for category in RiskCategory:
            category_factors = [f for f in risk_factors if f.category == category]
            if not category_factors:
                category_scores[category] = 0.0
                continue

            # Weighted average of risk scores
            total_weighted_score = sum(f.risk_score * f.weight for f in category_factors)
            total_weight = sum(f.weight for f in category_factors)

            if total_weight > 0:
                category_scores[category] = min(total_weighted_score / total_weight, 1.0)
            else:
                category_scores[category] = 0.0

        return category_scores

    def _calculate_overall_risk_score(self, category_scores: Dict[RiskCategory, float]) -> float:
        """Calculate overall risk score from category scores."""
        total_weighted_score = 0.0
        total_weight = 0.0

        for category, score in category_scores.items():
            weight = self.category_weights.get(category, 0.1)
            total_weighted_score += score * weight
            total_weight += weight

        return min(total_weighted_score / total_weight if total_weight > 0 else 0.0, 1.0)

    def _generate_recommendation(
        self,
        overall_score: float,
        category_scores: Dict[RiskCategory, float],
        risk_factors: List[RiskFactor]
    ) -> Tuple[str, List[str]]:
        """Generate recommendation and conditions based on risk assessment."""
        conditions = []

        # Check for critical factors
        critical_factors = [f for f in risk_factors if f.requires_attention]

        if overall_score > 0.8 or any(f.risk_score > 0.9 for f in critical_factors):
            return "decline", ["Risk level too high for acceptance"]

        elif overall_score > 0.6 or critical_factors:
            conditions = [
                "Senior attorney review required",
                "Additional risk mitigation measures needed"
            ]

            # Add specific conditions based on high-risk categories
            if category_scores.get(RiskCategory.CONFLICT, 0) > 0.7:
                conditions.append("Conflict resolution required before acceptance")

            if category_scores.get(RiskCategory.URGENCY, 0) > 0.7:
                conditions.append("Immediate attention and priority handling required")

            if category_scores.get(RiskCategory.FINANCIAL, 0) > 0.7:
                conditions.append("Financial arrangements must be secured")

            return "accept_with_conditions", conditions

        elif overall_score > 0.4:
            conditions = ["Standard risk management protocols apply"]
            return "accept_with_conditions", conditions

        else:
            return "accept", []

    def _generate_mitigation_strategies(self, risk_factors: List[RiskFactor]) -> List[str]:
        """Generate comprehensive mitigation strategies."""
        strategies = set()

        for factor in risk_factors:
            strategies.update(factor.mitigation_strategies)

        # Add general strategies based on risk patterns
        high_risk_factors = [f for f in risk_factors if f.risk_score > 0.7]
        if high_risk_factors:
            strategies.add("Regular case review and monitoring")
            strategies.add("Document all risk mitigation efforts")

        return sorted(list(strategies))

    def _estimate_resources(
        self,
        state: IntakeState,
        risk_factors: List[RiskFactor]
    ) -> Tuple[Optional[int], Optional[str]]:
        """Estimate resource requirements."""
        base_hours = 20  # Base case hours
        complexity_multiplier = 1.0

        # Adjust based on practice area
        if state.matter.practice_area == PracticeArea.PERSONAL_INJURY:
            base_hours = 30
        elif state.matter.practice_area == PracticeArea.FAMILY_LAW:
            base_hours = 25
        elif state.matter.practice_area == PracticeArea.CRIMINAL_DEFENSE:
            base_hours = 35

        # Adjust based on complexity factors
        complexity_factors = [f for f in risk_factors if f.category == RiskCategory.COMPLEXITY]
        if complexity_factors:
            avg_complexity = sum(f.risk_score for f in complexity_factors) / len(complexity_factors)
            complexity_multiplier = 1.0 + avg_complexity

        estimated_hours = int(base_hours * complexity_multiplier)

        # Determine complexity level
        if complexity_multiplier < 1.2:
            complexity_level = "low"
        elif complexity_multiplier < 1.5:
            complexity_level = "medium"
        elif complexity_multiplier < 2.0:
            complexity_level = "high"
        else:
            complexity_level = "very_high"

        return estimated_hours, complexity_level

    def _calculate_success_probability(self, risk_factors: List[RiskFactor]) -> float:
        """Calculate success probability based on risk factors."""
        success_factors = [f for f in risk_factors if f.category == RiskCategory.SUCCESS]

        if not success_factors:
            return 0.7  # Default moderate success probability

        # Average success risk (lower risk = higher success probability)
        avg_success_risk = sum(f.risk_score for f in success_factors) / len(success_factors)

        # Convert risk to success probability (inverse relationship)
        success_probability = 1.0 - avg_success_risk

        return max(min(success_probability, 0.95), 0.05)  # Clamp between 5% and 95%

    def _requires_immediate_attention(
        self,
        state: IntakeState,
        risk_factors: List[RiskFactor]
    ) -> bool:
        """Determine if case requires immediate attention."""
        # Check for critical urgency factors
        urgency_factors = [f for f in risk_factors if f.category == RiskCategory.URGENCY]
        if any(f.requires_attention for f in urgency_factors):
            return True

        # Check case urgency level
        if state.matter.urgency == CaseUrgency.CRITICAL:
            return True

        # Check for critical conflicts
        conflict_factors = [f for f in risk_factors if f.category == RiskCategory.CONFLICT]
        if any(f.requires_attention for f in conflict_factors):
            return True

        return False

    def _assess_deadline_risks(self, state: IntakeState) -> List[str]:
        """Assess deadline-related risks."""
        risks = []

        if state.matter.statute_of_limitations:
            days_until_sol = (state.matter.statute_of_limitations - datetime.now()).days
            if days_until_sol < 60:
                risks.append(f"Statute of limitations expires in {days_until_sol} days")

        if (state.matter.practice_area == PracticeArea.CRIMINAL_DEFENSE and
            state.matter.court_date):
            days_until_court = (state.matter.court_date - datetime.now()).days
            if days_until_court < 14:
                risks.append(f"Court date in {days_until_court} days")

        return risks

    def _generate_practice_area_insights(
        self,
        state: IntakeState,
        risk_factors: List[RiskFactor]
    ) -> Dict[str, Any]:
        """Generate practice area specific insights."""
        insights = {}

        if state.matter.practice_area == PracticeArea.PERSONAL_INJURY:
            insights["liability_assessment"] = "Requires thorough investigation"
            insights["insurance_considerations"] = "Verify all available coverage"
            insights["medical_documentation"] = "Ensure complete medical records"

        elif state.matter.practice_area == PracticeArea.FAMILY_LAW:
            insights["emotional_factors"] = "High emotional stakes require careful handling"
            insights["child_considerations"] = "Best interests of children paramount"
            insights["financial_complexity"] = "Asset division may require experts"

        elif state.matter.practice_area == PracticeArea.CRIMINAL_DEFENSE:
            insights["constitutional_rights"] = "Protect all constitutional rights"
            insights["prosecution_strength"] = "Assess strength of prosecution case"
            insights["plea_considerations"] = "Evaluate plea vs. trial options"

        return insights

    def _error_risk_result(self, error_message: str) -> RiskAssessmentResult:
        """Return high-risk result when assessment fails."""
        return RiskAssessmentResult(
            overall_risk_score=0.9,
            overall_risk_level=RiskLevel.VERY_HIGH,
            category_scores={category: 0.9 for category in RiskCategory},
            category_levels={category: RiskLevel.VERY_HIGH for category in RiskCategory},
            risk_factors=[
                RiskFactor(
                    category=RiskCategory.ETHICAL,
                    factor_name="System Error",
                    description=f"Risk assessment failed: {error_message}",
                    risk_score=0.9,
                    weight=1.0,
                    impact_description="Unable to properly assess risk",
                    mitigation_strategies=["Manual risk review required"],
                    requires_attention=True
                )
            ],
            high_risk_factors=[],
            recommendation="decline",
            conditions=["Manual risk assessment required"],
            mitigation_strategies=["System review needed"],
            requires_immediate_attention=True
        )
