"""
Test LLM Components for Enhanced Intake System

This module tests the LLM-powered components without requiring actual API calls
by using mock responses and validating the integration logic.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any

from .state import IntakeState, PracticeArea, CaseUrgency, ClientInformation, MatterInformation
from .conflict_types import ConflictSeverity, ConflictType
from .conflict_checker import MultiPracticeConflict<PERSON>he<PERSON>
from .risk_assessment import RiskAssessmentEngine


class TestLLMIntegration:
    """Test LLM integration without actual API calls."""
    
    def test_conflict_checker_initialization(self):
        """Test conflict checker initializes correctly with and without LLM."""
        # Test without LLM
        checker_no_llm = MultiPracticeConflictChecker(use_llm=False)
        assert checker_no_llm.use_llm is False
        assert not hasattr(checker_no_llm, 'llm_entity_extractor')
        
        # Test with LLM (should handle import gracefully)
        checker_with_llm = MultiPracticeConflictChecker(use_llm=True)
        # Should either have LLM components or gracefully fall back
        assert isinstance(checker_with_llm.use_llm, bool)
    
    def test_risk_assessor_initialization(self):
        """Test risk assessor initializes correctly with and without LLM."""
        # Test without LLM
        assessor_no_llm = RiskAssessmentEngine(use_llm=False)
        assert assessor_no_llm.use_llm is False
        
        # Test with LLM (should handle import gracefully)
        assessor_with_llm = RiskAssessmentEngine(use_llm=True)
        assert isinstance(assessor_with_llm.use_llm, bool)
    
    def test_conflict_types_enum(self):
        """Test conflict types enum values."""
        assert ConflictSeverity.LOW == "low"
        assert ConflictSeverity.CRITICAL == "critical"
        assert ConflictType.DIRECT_CLIENT == "direct_client"
        assert ConflictType.OPPOSING_PARTY == "opposing_party"
    
    @pytest.mark.asyncio
    async def test_conflict_checker_fallback_mode(self):
        """Test conflict checker works in fallback mode without LLM."""
        checker = MultiPracticeConflictChecker(use_llm=False)
        
        # Create test state
        client = ClientInformation(
            name="John Doe",
            email="<EMAIL>",
            phone="555-0123"
        )

        matter = MatterInformation(
            title="Test Personal Injury Case",
            description="Car accident case with injuries",
            practice_area=PracticeArea.PERSONAL_INJURY,
            urgency=CaseUrgency.MEDIUM
        )
        
        state = IntakeState(client=client, matter=matter)
        
        # Test conflict checking (should work without LLM)
        result = await checker.check_conflicts(state, "test_tenant")
        
        assert result is not None
        assert hasattr(result, 'has_conflicts')
        assert hasattr(result, 'conflicts')
        assert hasattr(result, 'checked_at')
    
    @pytest.mark.asyncio
    async def test_risk_assessor_fallback_mode(self):
        """Test risk assessor works in fallback mode without LLM."""
        assessor = RiskAssessmentEngine(use_llm=False)
        
        # Create test state
        client = ClientInformation(
            name="Jane Smith",
            email="<EMAIL>"
        )

        matter = MatterInformation(
            title="Test Family Law Case",
            description="Divorce with child custody issues",
            practice_area=PracticeArea.FAMILY_LAW,
            urgency=CaseUrgency.HIGH,
            estimated_value=100000.0
        )
        
        state = IntakeState(client=client, matter=matter)
        
        # Test risk assessment (should work without LLM)
        result = await assessor.assess_risk(state)
        
        assert result is not None
        assert hasattr(result, 'overall_risk_score')
        assert hasattr(result, 'overall_risk_level')
        assert hasattr(result, 'recommendation')
        assert 0.0 <= result.overall_risk_score <= 1.0


class TestEntityExtraction:
    """Test entity extraction logic without LLM calls."""
    
    def test_entity_patterns(self):
        """Test entity pattern initialization."""
        checker = MultiPracticeConflictChecker(use_llm=False)
        patterns = checker.entity_patterns
        
        assert "person_names" in patterns
        assert "companies" in patterns
        assert "legal_entities" in patterns
        assert "dates" in patterns
        assert "case_numbers" in patterns
        
        # Test that patterns are lists of strings
        for pattern_list in patterns.values():
            assert isinstance(pattern_list, list)
            for pattern in pattern_list:
                assert isinstance(pattern, str)


class TestStateModels:
    """Test state models and data structures."""
    
    def test_client_info_creation(self):
        """Test ClientInformation model creation."""
        client = ClientInformation(
            name="Test Client",
            email="<EMAIL>",
            phone="555-0123",
            address={"street": "123 Main St"}
        )
        
        assert client.name == "Test Client"
        assert client.email == "<EMAIL>"
        assert client.phone == "555-0123"
        assert client.address == {"street": "123 Main St"}

    def test_matter_info_creation(self):
        """Test MatterInformation model creation."""
        matter = MatterInformation(
            title="Test Matter",
            description="Test description",
            practice_area=PracticeArea.CRIMINAL_DEFENSE,
            urgency=CaseUrgency.CRITICAL,
            estimated_value=50000.0
        )
        
        assert matter.title == "Test Matter"
        assert matter.description == "Test description"
        assert matter.practice_area == PracticeArea.CRIMINAL_DEFENSE
        assert matter.urgency == CaseUrgency.CRITICAL
        assert matter.estimated_value == 50000.0
    
    def test_intake_state_creation(self):
        """Test IntakeState creation."""
        client = ClientInformation(name="Test Client")
        matter = MatterInformation(title="Test Matter")

        state = IntakeState(client=client, matter=matter)
        
        assert state.client == client
        assert state.matter == matter
        assert state.current_step == "initial_contact"
        assert state.can_proceed is True


class TestPracticeAreaSpecialization:
    """Test practice area specific functionality."""
    
    def test_practice_area_enum(self):
        """Test practice area enum values."""
        assert PracticeArea.PERSONAL_INJURY == "personal_injury"
        assert PracticeArea.FAMILY_LAW == "family_law"
        assert PracticeArea.CRIMINAL_DEFENSE == "criminal_defense"
    
    def test_case_urgency_enum(self):
        """Test case urgency enum values."""
        assert CaseUrgency.LOW == "low"
        assert CaseUrgency.MEDIUM == "medium"
        assert CaseUrgency.HIGH == "high"
        assert CaseUrgency.CRITICAL == "critical"


class TestErrorHandling:
    """Test error handling and graceful degradation."""
    
    @pytest.mark.asyncio
    async def test_conflict_checker_error_handling(self):
        """Test conflict checker handles errors gracefully."""
        checker = MultiPracticeConflictChecker(use_llm=False)
        
        # Test with minimal state that might cause issues
        client = ClientInformation()  # Empty client
        matter = MatterInformation()  # Empty matter
        state = IntakeState(client=client, matter=matter)
        
        # Should not raise exception
        result = await checker.check_conflicts(state, "test_tenant")
        assert result is not None
    
    @pytest.mark.asyncio
    async def test_risk_assessor_error_handling(self):
        """Test risk assessor handles errors gracefully."""
        assessor = RiskAssessmentEngine(use_llm=False)
        
        # Test with minimal state
        client = ClientInformation()
        matter = MatterInformation()
        state = IntakeState(client=client, matter=matter)
        
        # Should not raise exception
        result = await assessor.assess_risk(state)
        assert result is not None


# Integration test that can be run manually
async def manual_integration_test():
    """Manual integration test for the LLM system."""
    print("🧪 Running Manual Integration Test")
    
    # Test case setup
    client = ClientInformation(
        name="Sarah Johnson",
        email="<EMAIL>",
        phone="555-0123"
    )

    matter = MatterInformation(
        title="Motor Vehicle Accident",
        description="""
        I was in a car accident last week. The other driver ran a red light
        and hit my car. I have injuries including back pain and a concussion.
        The other driver's insurance company is State Farm. There were witnesses
        at the scene. I've been treated by Dr. Smith at Austin Medical Center.
        """,
        practice_area=PracticeArea.PERSONAL_INJURY,
        urgency=CaseUrgency.HIGH,
        estimated_value=75000.0,
        incident_date=datetime.now() - timedelta(days=7)
    )
    
    state = IntakeState(client=client, matter=matter)
    
    # Test conflict checking
    print("🔍 Testing Conflict Checking...")
    conflict_checker = MultiPracticeConflictChecker(use_llm=False)
    conflict_result = await conflict_checker.check_conflicts(state, "test_tenant")
    print(f"   Conflicts found: {conflict_result.has_conflicts}")
    
    # Test risk assessment
    print("📊 Testing Risk Assessment...")
    risk_assessor = RiskAssessmentEngine(use_llm=False)
    risk_result = await risk_assessor.assess_risk(state, conflict_result)
    print(f"   Risk level: {risk_result.overall_risk_level.value}")
    print(f"   Recommendation: {risk_result.recommendation}")
    
    print("✅ Manual Integration Test Complete")


if __name__ == "__main__":
    # Run manual test
    asyncio.run(manual_integration_test())
