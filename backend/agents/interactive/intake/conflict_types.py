"""
Shared Conflict Types and Enums

This module contains shared enums and types used across conflict checking
and analysis components to avoid circular imports.
"""

from enum import Enum


class ConflictSeverity(str, Enum):
    """Enhanced severity levels for conflicts with detailed risk assessment."""
    LOW = "low"           # Minor conflict, may be waivable with client consent
    MEDIUM = "medium"     # Significant conflict, requires detailed review and analysis
    HIGH = "high"         # Serious conflict, likely not waivable, requires senior review
    CRITICAL = "critical" # Absolute conflict, cannot represent, immediate escalation required


class ConflictType(str, Enum):
    """Types of conflicts that can be detected."""
    DIRECT_CLIENT = "direct_client"                    # Same client, different matter
    OPPOSING_PARTY = "opposing_party"                  # Client is opposing party in existing matter
    FAMILY_MEMBER = "family_member"                    # Family relationship conflict
    BUSINESS_RELATIONSHIP = "business_relationship"     # Business/corporate relationship
    INSURANCE_COMPANY = "insurance_company"            # Insurance company representation
    MEDICAL_PROVIDER = "medical_provider"              # Medical provider representation
    CO_DEFENDANT = "co_defendant"                      # Co-defendant in criminal matter
    WITNESS = "witness"                                # Witness in existing matter
    JURISDICTION = "jurisdiction"                      # Jurisdictional conflict
    ETHICAL = "ethical"                                # Ethical rule violation
    FINANCIAL = "financial"                            # Financial interest conflict


class ConflictCategory(str, Enum):
    """Categories of conflicts for detailed analysis."""
    DIRECT_REPRESENTATION = "direct_representation"
    ADVERSE_REPRESENTATION = "adverse_representation"
    POSITIONAL_CONFLICT = "positional_conflict"
    ISSUE_CONFLICT = "issue_conflict"
    IMPUTED_CONFLICT = "imputed_conflict"
    BUSINESS_TRANSACTION = "business_transaction"
    PERSONAL_INTEREST = "personal_interest"
    FORMER_CLIENT = "former_client"
    PROSPECTIVE_CLIENT = "prospective_client"
    THIRD_PARTY_INTEREST = "third_party_interest"


class EthicalRule(str, Enum):
    """Relevant ethical rules for conflict analysis."""
    RULE_1_7 = "rule_1_7"  # Conflict of Interest: Current Clients
    RULE_1_8 = "rule_1_8"  # Conflict of Interest: Prohibited Transactions
    RULE_1_9 = "rule_1_9"  # Duties to Former Clients
    RULE_1_10 = "rule_1_10"  # Imputation of Conflicts
    RULE_1_11 = "rule_1_11"  # Special Conflicts for Former Government Officers
    RULE_1_12 = "rule_1_12"  # Former Judge, Arbitrator, Mediator
    RULE_1_13 = "rule_1_13"  # Organization as Client
    RULE_1_18 = "rule_1_18"  # Duties to Prospective Client
