"""
LLM-Powered Conflict Analysis Engine

This module provides sophisticated conflict of interest analysis using Large Language Models
to understand complex legal relationships, identify potential conflicts, and provide detailed
ethical guidance with legal reasoning capabilities.

Key Features:
- Advanced relationship analysis and conflict detection
- Ethical rule interpretation and application
- Context-aware conflict severity assessment
- Detailed legal reasoning for conflict determinations
- Waiver analysis and recommendations
- Cross-jurisdictional conflict considerations
- Practice-area specific conflict rules
- Mitigation strategy recommendations
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import PydanticOutputParser
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field

from .state import PracticeArea, IntakeState
from .llm_entity_extractor import EntityExtractionResult, ExtractedEntity
from .conflict_checker import ConflictSeverity, ConflictType

logger = logging.getLogger(__name__)


class ConflictCategory(str, Enum):
    """Categories of conflicts for detailed analysis."""
    DIRECT_REPRESENTATION = "direct_representation"
    ADVERSE_REPRESENTATION = "adverse_representation"
    POSITIONAL_CONFLICT = "positional_conflict"
    ISSUE_CONFLICT = "issue_conflict"
    IMPUTED_CONFLICT = "imputed_conflict"
    BUSINESS_TRANSACTION = "business_transaction"
    PERSONAL_INTEREST = "personal_interest"
    FORMER_CLIENT = "former_client"
    PROSPECTIVE_CLIENT = "prospective_client"
    THIRD_PARTY_INTEREST = "third_party_interest"


class EthicalRule(str, Enum):
    """Relevant ethical rules for conflict analysis."""
    RULE_1_7 = "rule_1_7"  # Conflict of Interest: Current Clients
    RULE_1_8 = "rule_1_8"  # Conflict of Interest: Prohibited Transactions
    RULE_1_9 = "rule_1_9"  # Duties to Former Clients
    RULE_1_10 = "rule_1_10"  # Imputation of Conflicts
    RULE_1_11 = "rule_1_11"  # Special Conflicts for Former Government Officers
    RULE_1_12 = "rule_1_12"  # Former Judge, Arbitrator, Mediator
    RULE_1_13 = "rule_1_13"  # Organization as Client
    RULE_1_18 = "rule_1_18"  # Duties to Prospective Client


@dataclass
class ConflictAnalysis:
    """Detailed conflict analysis result."""
    conflict_exists: bool
    conflict_category: ConflictCategory
    conflict_type: ConflictType
    severity: ConflictSeverity
    confidence: float  # 0.0 to 1.0
    
    # Detailed analysis
    description: str
    legal_reasoning: str
    applicable_rules: List[EthicalRule]
    
    # Entities involved
    conflicting_entities: List[str]
    affected_matters: List[str]
    
    # Waiver analysis
    waivable: bool
    waiver_requirements: List[str]
    waiver_likelihood: str  # "high", "medium", "low", "impossible"
    
    # Recommendations
    recommended_action: str  # "decline", "waiver", "screen", "proceed"
    mitigation_strategies: List[str]
    escalation_required: bool
    
    # Additional context
    jurisdictional_considerations: List[str]
    practice_area_specific_factors: List[str]
    temporal_factors: List[str]


class LLMConflictAnalysisOutput(BaseModel):
    """Structured output for LLM conflict analysis."""
    conflicts_identified: List[Dict[str, Any]] = Field(description="List of identified conflicts with detailed analysis")
    overall_conflict_assessment: Dict[str, Any] = Field(description="Overall conflict assessment and recommendation")
    entity_relationship_analysis: List[Dict[str, Any]] = Field(description="Analysis of entity relationships and potential conflicts")
    ethical_rule_analysis: List[Dict[str, Any]] = Field(description="Analysis of applicable ethical rules")
    waiver_analysis: Dict[str, Any] = Field(description="Analysis of conflict waiver possibilities")
    jurisdictional_factors: List[str] = Field(description="Jurisdictional considerations affecting conflicts")
    practice_area_factors: List[str] = Field(description="Practice area specific conflict factors")
    temporal_considerations: List[str] = Field(description="Time-based factors affecting conflicts")
    mitigation_strategies: List[str] = Field(description="Strategies to mitigate identified conflicts")
    recommendations: Dict[str, Any] = Field(description="Detailed recommendations for handling conflicts")
    confidence_assessment: float = Field(description="Overall confidence in conflict analysis (0.0-1.0)")
    legal_reasoning: str = Field(description="Detailed legal reasoning for conflict determinations")


class LLMConflictAnalyzer:
    """
    Advanced LLM-powered conflict analysis for legal intake.
    
    Uses sophisticated language models to analyze potential conflicts of interest
    with deep understanding of ethical rules, legal relationships, and practice
    area specific considerations.
    """
    
    def __init__(self, llm_model: str = "gpt-4", temperature: float = 0.1):
        """Initialize the LLM conflict analyzer."""
        self.llm = ChatOpenAI(model=llm_model, temperature=temperature)
        self.output_parser = PydanticOutputParser(pydantic_object=LLMConflictAnalysisOutput)
        
        self.analysis_prompt = ChatPromptTemplate.from_messages([
            ("system", self._get_system_prompt()),
            ("human", self._get_human_prompt())
        ])
    
    async def analyze_conflicts(
        self,
        intake_state: IntakeState,
        entity_extraction: EntityExtractionResult,
        existing_clients: List[Dict[str, Any]] = None,
        existing_matters: List[Dict[str, Any]] = None,
        tenant_id: str = None
    ) -> List[ConflictAnalysis]:
        """
        Perform comprehensive conflict analysis using LLM reasoning.
        
        Args:
            intake_state: Current intake state
            entity_extraction: Results from entity extraction
            existing_clients: List of existing clients
            existing_matters: List of existing matters
            tenant_id: Tenant ID for context
            
        Returns:
            List of ConflictAnalysis results
        """
        try:
            # Prepare comprehensive context for LLM
            context = self._prepare_analysis_context(
                intake_state, entity_extraction, existing_clients, existing_matters
            )
            
            # Create the prompt
            prompt = self.analysis_prompt.format_prompt(
                case_description=intake_state.matter.description or "",
                client_information=self._format_client_info(intake_state.client),
                extracted_entities=self._format_entities(entity_extraction.entities),
                entity_relationships=self._format_relationships(entity_extraction.relationships),
                existing_clients=self._format_existing_clients(existing_clients or []),
                existing_matters=self._format_existing_matters(existing_matters or []),
                practice_area=intake_state.matter.practice_area.value if intake_state.matter.practice_area else "unknown",
                additional_context=context,
                format_instructions=self.output_parser.get_format_instructions()
            )
            
            # Get LLM response
            response = await self.llm.ainvoke(prompt.to_messages())
            
            # Parse structured output
            parsed_result = self.output_parser.parse(response.content)
            
            # Convert to our internal format
            conflict_analyses = self._convert_llm_output(parsed_result, intake_state)
            
            logger.info(f"Analyzed conflicts: {len(conflict_analyses)} potential conflicts identified")
            
            return conflict_analyses
            
        except Exception as e:
            logger.error(f"Error during LLM conflict analysis: {str(e)}")
            return [self._error_conflict_analysis(str(e))]
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for conflict analysis."""
        return """You are an expert legal ethics specialist with deep knowledge of professional responsibility rules, conflict of interest analysis, and legal practice management. Your task is to analyze potential conflicts of interest in legal matters with the highest level of accuracy and ethical compliance.

CONFLICT ANALYSIS FRAMEWORK:

1. ETHICAL RULES ANALYSIS:
   - Rule 1.7: Conflict of Interest: Current Clients
   - Rule 1.8: Conflict of Interest: Prohibited Transactions  
   - Rule 1.9: Duties to Former Clients
   - Rule 1.10: Imputation of Conflicts of Interest
   - Rule 1.11: Special Conflicts of Interest for Former Government Officers
   - Rule 1.12: Former Judge, Arbitrator, Mediator, or Other Third-Party Neutral
   - Rule 1.13: Organization as Client
   - Rule 1.18: Duties to Prospective Client

2. CONFLICT CATEGORIES TO ASSESS:
   - Direct representation conflicts (same client, different matter)
   - Adverse representation conflicts (representing opposing parties)
   - Positional conflicts (taking inconsistent legal positions)
   - Issue conflicts (material limitation on representation)
   - Imputed conflicts (conflicts affecting entire firm)
   - Business transaction conflicts (lawyer's personal interests)
   - Former client conflicts (substantially related matters)
   - Prospective client conflicts (confidential information received)

3. RELATIONSHIP ANALYSIS:
   - Analyze all entity relationships for potential conflicts
   - Consider family relationships, business relationships, professional relationships
   - Evaluate adversarial relationships and their implications
   - Assess third-party interests and influences

4. PRACTICE AREA CONSIDERATIONS:
   - Personal Injury: Insurance company relationships, medical provider conflicts, opposing party representation
   - Family Law: Dual representation issues, child interests, domestic violence considerations
   - Criminal Defense: Co-defendant conflicts, victim representation, prosecution relationships

5. WAIVER ANALYSIS:
   - Determine if conflicts are waivable under applicable rules
   - Assess likelihood of obtaining valid informed consent
   - Consider whether representation is prohibited regardless of consent
   - Evaluate practical considerations for waiver

6. SEVERITY ASSESSMENT:
   - CRITICAL: Absolute prohibition, cannot represent under any circumstances
   - HIGH: Serious conflict, waiver unlikely or inadvisable
   - MEDIUM: Significant conflict, waiver possible with conditions
   - LOW: Minor conflict, easily waivable with proper consent

7. MITIGATION STRATEGIES:
   - Screening procedures and ethical walls
   - Withdrawal from conflicting representations
   - Referral to other counsel
   - Client consent and waiver procedures
   - Ongoing monitoring and compliance

ANALYSIS REQUIREMENTS:
- Provide detailed legal reasoning for all determinations
- Cite applicable ethical rules and precedents
- Consider jurisdictional variations in conflict rules
- Assess both current and potential future conflicts
- Evaluate practical and strategic considerations
- Maintain conservative approach to conflict identification
- Prioritize client protection and ethical compliance"""
    
    def _get_human_prompt(self) -> str:
        """Get the human prompt template."""
        return """Please perform a comprehensive conflict of interest analysis for this legal matter:

CASE DESCRIPTION:
{case_description}

CLIENT INFORMATION:
{client_information}

EXTRACTED ENTITIES:
{extracted_entities}

ENTITY RELATIONSHIPS:
{entity_relationships}

EXISTING CLIENTS:
{existing_clients}

EXISTING MATTERS:
{existing_matters}

PRACTICE AREA:
{practice_area}

ADDITIONAL CONTEXT:
{additional_context}

Perform thorough conflict analysis following these steps:

1. ENTITY RELATIONSHIP ANALYSIS: Analyze all relationships between extracted entities and existing clients/matters
2. ETHICAL RULE APPLICATION: Apply relevant professional responsibility rules to identify conflicts
3. CONFLICT CATEGORIZATION: Classify any identified conflicts by type and category
4. SEVERITY ASSESSMENT: Determine severity level and potential impact
5. WAIVER ANALYSIS: Assess whether conflicts can be waived and likelihood of obtaining consent
6. JURISDICTIONAL CONSIDERATIONS: Consider jurisdiction-specific conflict rules
7. PRACTICE AREA FACTORS: Apply practice area specific conflict considerations
8. TEMPORAL ANALYSIS: Consider timing factors and potential future conflicts
9. MITIGATION STRATEGIES: Recommend strategies to address identified conflicts
10. FINAL RECOMMENDATIONS: Provide clear recommendations for handling the matter

Provide your analysis in the following structured format:
{format_instructions}

Maintain the highest ethical standards and err on the side of caution in conflict identification."""

    def _prepare_analysis_context(
        self,
        intake_state: IntakeState,
        entity_extraction: EntityExtractionResult,
        existing_clients: List[Dict[str, Any]],
        existing_matters: List[Dict[str, Any]]
    ) -> str:
        """Prepare comprehensive context for conflict analysis."""
        context_parts = []

        # Add urgency information
        if intake_state.matter.urgency:
            context_parts.append(f"Case Urgency: {intake_state.matter.urgency.value}")

        # Add temporal factors
        if intake_state.matter.statute_of_limitations:
            context_parts.append(f"Statute of Limitations: {intake_state.matter.statute_of_limitations}")

        if intake_state.matter.court_date:
            context_parts.append(f"Court Date: {intake_state.matter.court_date}")

        # Add complexity indicators
        if entity_extraction.complexity_indicators:
            context_parts.append(f"Complexity Indicators: {', '.join(entity_extraction.complexity_indicators)}")

        # Add potential conflicts from entity extraction
        if entity_extraction.potential_conflicts:
            context_parts.append(f"Potential Conflicts Identified: {', '.join(entity_extraction.potential_conflicts)}")

        return "\n".join(context_parts) if context_parts else "No additional context available."

    def _format_client_info(self, client) -> str:
        """Format client information for analysis."""
        info_parts = []

        if client.name:
            info_parts.append(f"Name: {client.name}")
        if client.email:
            info_parts.append(f"Email: {client.email}")
        if client.phone:
            info_parts.append(f"Phone: {client.phone}")
        if client.address:
            info_parts.append(f"Address: {client.address}")

        return "\n".join(info_parts) if info_parts else "Limited client information available."

    def _format_entities(self, entities: List[ExtractedEntity]) -> str:
        """Format extracted entities for analysis."""
        if not entities:
            return "No entities extracted."

        entity_lines = []
        for entity in entities:
            line = f"- {entity.name} ({entity.entity_type.value}, {entity.role.value})"
            if entity.confidence < 0.7:
                line += f" [Low Confidence: {entity.confidence:.2f}]"
            if entity.potential_conflicts:
                line += f" [Potential Conflicts: {', '.join(entity.potential_conflicts)}]"
            entity_lines.append(line)

        return "\n".join(entity_lines)

    def _format_relationships(self, relationships: List[Tuple[str, str, Any, float]]) -> str:
        """Format entity relationships for analysis."""
        if not relationships:
            return "No relationships identified."

        rel_lines = []
        for entity1, entity2, rel_type, confidence in relationships:
            line = f"- {entity1} → {entity2} ({rel_type.value if hasattr(rel_type, 'value') else rel_type})"
            if confidence < 0.7:
                line += f" [Confidence: {confidence:.2f}]"
            rel_lines.append(line)

        return "\n".join(rel_lines)

    def _format_existing_clients(self, existing_clients: List[Dict[str, Any]]) -> str:
        """Format existing clients for analysis."""
        if not existing_clients:
            return "No existing clients provided for comparison."

        client_lines = []
        for client in existing_clients[:10]:  # Limit to first 10 for prompt size
            name = client.get("name", "Unknown")
            matters = client.get("matters", [])
            line = f"- {name}"
            if matters:
                line += f" (Matters: {len(matters)})"
            client_lines.append(line)

        if len(existing_clients) > 10:
            client_lines.append(f"... and {len(existing_clients) - 10} more clients")

        return "\n".join(client_lines)

    def _format_existing_matters(self, existing_matters: List[Dict[str, Any]]) -> str:
        """Format existing matters for analysis."""
        if not existing_matters:
            return "No existing matters provided for comparison."

        matter_lines = []
        for matter in existing_matters[:10]:  # Limit to first 10 for prompt size
            title = matter.get("title", "Unknown Matter")
            practice_area = matter.get("practice_area", "Unknown")
            client_name = matter.get("client_name", "Unknown Client")
            line = f"- {title} ({practice_area}) - Client: {client_name}"
            matter_lines.append(line)

        if len(existing_matters) > 10:
            matter_lines.append(f"... and {len(existing_matters) - 10} more matters")

        return "\n".join(matter_lines)

    def _convert_llm_output(
        self,
        llm_output: LLMConflictAnalysisOutput,
        intake_state: IntakeState
    ) -> List[ConflictAnalysis]:
        """Convert LLM output to ConflictAnalysis objects."""
        conflict_analyses = []

        for conflict_data in llm_output.conflicts_identified:
            try:
                analysis = ConflictAnalysis(
                    conflict_exists=conflict_data.get("exists", False),
                    conflict_category=ConflictCategory(conflict_data.get("category", "direct_representation")),
                    conflict_type=ConflictType(conflict_data.get("type", "direct_client")),
                    severity=ConflictSeverity(conflict_data.get("severity", "medium")),
                    confidence=conflict_data.get("confidence", 0.5),
                    description=conflict_data.get("description", ""),
                    legal_reasoning=conflict_data.get("legal_reasoning", ""),
                    applicable_rules=[EthicalRule(rule) for rule in conflict_data.get("applicable_rules", [])],
                    conflicting_entities=conflict_data.get("conflicting_entities", []),
                    affected_matters=conflict_data.get("affected_matters", []),
                    waivable=conflict_data.get("waivable", False),
                    waiver_requirements=conflict_data.get("waiver_requirements", []),
                    waiver_likelihood=conflict_data.get("waiver_likelihood", "low"),
                    recommended_action=conflict_data.get("recommended_action", "decline"),
                    mitigation_strategies=conflict_data.get("mitigation_strategies", []),
                    escalation_required=conflict_data.get("escalation_required", True),
                    jurisdictional_considerations=llm_output.jurisdictional_factors,
                    practice_area_specific_factors=llm_output.practice_area_factors,
                    temporal_factors=llm_output.temporal_considerations
                )
                conflict_analyses.append(analysis)
            except (ValueError, KeyError) as e:
                logger.warning(f"Error converting conflict analysis: {e}")
                continue

        return conflict_analyses

    def _error_conflict_analysis(self, error_message: str) -> ConflictAnalysis:
        """Return error conflict analysis when LLM fails."""
        return ConflictAnalysis(
            conflict_exists=True,
            conflict_category=ConflictCategory.DIRECT_REPRESENTATION,
            conflict_type=ConflictType.ETHICAL,
            severity=ConflictSeverity.CRITICAL,
            confidence=0.0,
            description=f"Conflict analysis failed: {error_message}",
            legal_reasoning="Unable to perform conflict analysis due to system error",
            applicable_rules=[EthicalRule.RULE_1_7],
            conflicting_entities=[],
            affected_matters=[],
            waivable=False,
            waiver_requirements=[],
            waiver_likelihood="impossible",
            recommended_action="decline",
            mitigation_strategies=["Manual conflict review required"],
            escalation_required=True,
            jurisdictional_considerations=["System error - manual review needed"],
            practice_area_specific_factors=["Analysis failed"],
            temporal_factors=[]
        )
